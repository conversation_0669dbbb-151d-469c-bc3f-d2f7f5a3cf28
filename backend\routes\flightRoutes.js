const express = require('express');
const { body, query } = require('express-validator');
const {
  searchFlights,
  getFlightById,
  getAllFlights,
  createFlight,
  updateFlight,
  deleteFlight,
  updateFlightStatus,
  getFlightsByRoute,
  getFlightStats
} = require('../controllers/flightController');
const { protect, authorize } = require('../middlewares/auth');

const router = express.Router();

// Validation middleware
const validateFlightSearch = [
  body('from')
    .isLength({ min: 3, max: 3 })
    .withMessage('رمز مطار المغادرة يجب أن يكون 3 أحرف'),
  body('to')
    .isLength({ min: 3, max: 3 })
    .withMessage('رمز مطار الوصول يجب أن يكون 3 أحرف'),
  body('departureDate')
    .isISO8601()
    .withMessage('تاريخ المغادرة غير صحيح'),
  body('passengers')
    .optional()
    .isInt({ min: 1, max: 9 })
    .withMessage('عدد المسافرين يجب أن يكون بين 1 و 9'),
  body('class')
    .optional()
    .isIn(['economy', 'business', 'first'])
    .withMessage('درجة السفر غير صحيحة')
];

const validateFlightCreation = [
  body('flightNumber')
    .matches(/^[A-Z]{2}[0-9]{3,4}$/)
    .withMessage('رقم الرحلة غير صحيح'),
  body('airline.name')
    .notEmpty()
    .withMessage('اسم شركة الطيران مطلوب'),
  body('airline.code')
    .isLength({ min: 2, max: 3 })
    .withMessage('رمز شركة الطيران غير صحيح'),
  body('route.departure.airport.code')
    .isLength({ min: 3, max: 3 })
    .withMessage('رمز مطار المغادرة غير صحيح'),
  body('route.arrival.airport.code')
    .isLength({ min: 3, max: 3 })
    .withMessage('رمز مطار الوصول غير صحيح'),
  body('route.departure.scheduledTime')
    .isISO8601()
    .withMessage('وقت المغادرة غير صحيح'),
  body('route.arrival.scheduledTime')
    .isISO8601()
    .withMessage('وقت الوصول غير صحيح')
];

const validateStatusUpdate = [
  body('status')
    .isIn(['scheduled', 'delayed', 'cancelled', 'boarding', 'departed', 'arrived'])
    .withMessage('حالة الرحلة غير صحيحة')
];

const validatePagination = [
  query('page')
    .optional()
    .isInt({ min: 1 })
    .withMessage('رقم الصفحة يجب أن يكون رقم موجب'),
  query('limit')
    .optional()
    .isInt({ min: 1, max: 100 })
    .withMessage('عدد النتائج يجب أن يكون بين 1 و 100')
];

// Public routes
router.post('/search', validateFlightSearch, searchFlights);
router.get('/route/:from/:to', getFlightsByRoute);
router.get('/stats', protect, authorize('admin'), getFlightStats);
router.get('/:id', getFlightById);
router.get('/', validatePagination, getAllFlights);

// Admin routes
router.post('/', protect, authorize('admin'), validateFlightCreation, createFlight);
router.put('/:id', protect, authorize('admin'), updateFlight);
router.patch('/:id/status', protect, authorize('admin'), validateStatusUpdate, updateFlightStatus);
router.delete('/:id', protect, authorize('admin'), deleteFlight);

module.exports = router;
