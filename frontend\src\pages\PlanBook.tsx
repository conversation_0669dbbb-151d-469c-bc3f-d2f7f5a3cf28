import React, { useState } from 'react';
import { useTranslation } from 'react-i18next';
import { Link } from 'react-router-dom';
import {
  CreditCard,
  Clock,
  FileCheck,
  Shield,
  Hotel,
  Car,
  Route,
  Package,
  Heart,
  DollarSign
} from 'lucide-react';

interface TabContent {
  id: string;
  title: string;
  subtitle: string;
  items: Array<{
    icon: React.ReactNode;
    title: string;
    description: string;
    image: string;
    link: string;
  }>;
}

const PlanBook: React.FC = () => {
  const { t, i18n } = useTranslation();
  const [activeTab, setActiveTab] = useState('planJourney');
  const isRTL = i18n.language === 'ar';

  const tabs = [
    {
      id: 'planJourney',
      label: t('planBook.planJourney.title'),
      shortLabel: t('planBook.planJourney.shortLabel')
    },
    {
      id: 'book',
      label: t('planBook.book.title'),
      shortLabel: t('planBook.book.shortLabel')
    },
    {
      id: 'beforeFly',
      label: t('planBook.beforeFly.title'),
      shortLabel: t('planBook.beforeFly.shortLabel')
    }
  ];

  const tabContent: Record<string, TabContent> = {
    planJourney: {
      id: 'planJourney',
      title: t('planBook.planJourney.title'),
      subtitle: t('planBook.planJourney.subtitle'),
      items: [
        {
          icon: <DollarSign className="h-8 w-8" />,
          title: t('planBook.planJourney.priceTypes'),
          description: t('planBook.planJourney.priceTypesDesc'),
          image: 'https://images.unsplash.com/photo-1556742049-0cfed4f6a45d?ixlib=rb-4.0.3&auto=format&fit=crop&w=2340&q=80',
          link: '/price-types'
        },
        {
          icon: <CreditCard className="h-8 w-8" />,
          title: t('planBook.planJourney.paymentMethods'),
          description: t('planBook.planJourney.paymentMethodsDesc'),
          image: 'https://images.unsplash.com/photo-1563013544-824ae1b704d3?ixlib=rb-4.0.3&auto=format&fit=crop&w=2340&q=80',
          link: '/payment-methods'
        },
        {
          icon: <Clock className="h-8 w-8" />,
          title: t('planBook.planJourney.lowPrices'),
          description: t('planBook.planJourney.lowPricesDesc'),
          image: 'https://images.unsplash.com/photo-1551836022-deb4988cc6c0?ixlib=rb-4.0.3&auto=format&fit=crop&w=2340&q=80',
          link: '/low-prices'
        }
      ]
    },
    book: {
      id: 'book',
      title: t('planBook.book.title'),
      subtitle: t('planBook.book.subtitle'),
      items: [
        {
          icon: <Hotel className="h-8 w-8" />,
          title: t('planBook.book.hotels'),
          description: t('planBook.book.hotelsDesc'),
          image: 'https://images.unsplash.com/photo-1566073771259-6a8506099945?ixlib=rb-4.0.3&auto=format&fit=crop&w=2340&q=80',
          link: '/hotels'
        },
        {
          icon: <Car className="h-8 w-8" />,
          title: t('planBook.book.cars'),
          description: t('planBook.book.carsDesc'),
          image: 'https://images.unsplash.com/photo-1549924231-f129b911e442?ixlib=rb-4.0.3&auto=format&fit=crop&w=2340&q=80',
          link: '/car-rental'
        },
        {
          icon: <Route className="h-8 w-8" />,
          title: t('planBook.book.transfers'),
          description: t('planBook.book.transfersDesc'),
          image: 'https://images.unsplash.com/photo-1449824913935-59a10b8d2000?ixlib=rb-4.0.3&auto=format&fit=crop&w=2340&q=80',
          link: '/airport-transfers'
        },
        {
          icon: <Package className="h-8 w-8" />,
          title: t('planBook.book.shipping'),
          description: t('planBook.book.shippingDesc'),
          image: 'https://images.unsplash.com/photo-1578662996442-48f60103fc96?ixlib=rb-4.0.3&auto=format&fit=crop&w=2340&q=80',
          link: '/shipping'
        }
      ]
    },
    beforeFly: {
      id: 'beforeFly',
      title: t('planBook.beforeFly.title'),
      subtitle: t('planBook.beforeFly.subtitle'),
      items: [
        {
          icon: <FileCheck className="h-8 w-8" />,
          title: t('planBook.beforeFly.checkin'),
          description: t('planBook.beforeFly.checkinDesc'),
          image: 'https://images.unsplash.com/photo-1436491865332-7a61a109cc05?ixlib=rb-4.0.3&auto=format&fit=crop&w=2340&q=80',
          link: '/checkin-info'
        },
        {
          icon: <Shield className="h-8 w-8" />,
          title: t('planBook.beforeFly.visas'),
          description: t('planBook.beforeFly.visasDesc'),
          image: 'https://images.unsplash.com/photo-1554224155-6726b3ff858f?ixlib=rb-4.0.3&auto=format&fit=crop&w=2340&q=80',
          link: '/visa-info'
        },
        {
          icon: <Heart className="h-8 w-8" />,
          title: t('planBook.beforeFly.vaccinations'),
          description: t('planBook.beforeFly.vaccinationsDesc'),
          image: 'https://images.unsplash.com/photo-1559757148-5c350d0d3c56?ixlib=rb-4.0.3&auto=format&fit=crop&w=2340&q=80',
          link: '/vaccinations'
        }
      ]
    }
  };

  const currentContent = tabContent[activeTab];

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Hero Section */}
      <section className="bg-gradient-to-r from-blue-600 to-indigo-700 text-white py-16">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center">
            <h1 className="text-4xl md:text-5xl font-bold mb-4">
              {t('planBook.title')}
            </h1>
            <p className="text-xl text-blue-100 max-w-3xl mx-auto">
              {t('planBook.subtitle')}
            </p>
          </div>
        </div>
      </section>

      {/* Navigation Tabs */}
      <section className="bg-white shadow-sm sticky top-0 z-40">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <nav
            className="flex space-x-8 rtl:space-x-reverse overflow-x-auto"
            role="tablist"
            aria-label={t('planBook.title')}
          >
            {tabs.map((tab) => (
              <button
                key={tab.id}
                onClick={() => setActiveTab(tab.id)}
                role="tab"
                aria-selected={activeTab === tab.id}
                aria-controls={`tabpanel-${tab.id}`}
                className={`py-4 px-2 text-sm font-medium border-b-2 whitespace-nowrap transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 ${
                  activeTab === tab.id
                    ? 'border-blue-600 text-blue-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                }`}
              >
                <span className="hidden md:inline">{tab.label}</span>
                <span className="md:hidden">{tab.shortLabel}</span>
              </button>
            ))}
          </nav>
        </div>
      </section>

      {/* Content Section */}
      <section className="py-16">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div
            id={`tabpanel-${activeTab}`}
            role="tabpanel"
            aria-labelledby={`tab-${activeTab}`}
          >
            <div className="text-center mb-12">
              <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
                {currentContent.title}
              </h2>
              <p className="text-lg text-gray-600 max-w-3xl mx-auto">
                {currentContent.subtitle}
              </p>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
              {currentContent.items.map((item, index) => (
                <article
                  key={index}
                  className="bg-white rounded-xl shadow-lg overflow-hidden hover:shadow-xl transition-shadow duration-300 group"
                >
                  <div className="relative">
                    <img
                      src={item.image}
                      alt={item.title}
                      className="w-full h-48 object-cover group-hover:scale-105 transition-transform duration-300"
                      loading="lazy"
                    />
                    <div className="absolute inset-0 bg-gradient-to-t from-black via-transparent to-transparent opacity-60"></div>
                    <div className="absolute bottom-4 left-4 rtl:right-4 rtl:left-auto text-white">
                      <div className="mb-2" aria-hidden="true">
                        {item.icon}
                      </div>
                    </div>
                  </div>

                  <div className="p-6">
                    <h3 className="text-xl font-bold text-gray-900 mb-3">
                      {item.title}
                    </h3>
                    <p className="text-gray-600 mb-4">
                      {item.description}
                    </p>

                    <Link
                      to={item.link}
                      className="inline-flex items-center justify-center w-full bg-blue-600 text-white py-2 px-4 rounded-lg hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 transition-colors duration-200 font-medium"
                      aria-label={`${t('planBook.learnMore')} - ${item.title}`}
                    >
                      {t('planBook.learnMore')}
                    </Link>
                  </div>
                </article>
              ))}
            </div>
          </div>
        </div>
      </section>

      {/* Call to Action */}
      <section className="bg-gradient-to-r from-indigo-600 to-blue-700 text-white py-16">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <h2 className="text-3xl md:text-4xl font-bold mb-4">
            {t('planBook.helpTitle')}
          </h2>
          <p className="text-xl mb-8 text-blue-100">
            {t('planBook.helpSubtitle')}
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <button
              className="bg-white text-blue-600 px-8 py-3 rounded-lg font-semibold hover:bg-gray-100 focus:outline-none focus:ring-2 focus:ring-white focus:ring-offset-2 focus:ring-offset-blue-600 transition-colors duration-200"
              aria-label={t('planBook.talkToExpert')}
            >
              {t('planBook.talkToExpert')}
            </button>
            <button
              className="border-2 border-white text-white px-8 py-3 rounded-lg font-semibold hover:bg-white hover:text-blue-600 focus:outline-none focus:ring-2 focus:ring-white focus:ring-offset-2 focus:ring-offset-blue-600 transition-colors duration-200"
              aria-label={t('planBook.freeGuide')}
            >
              {t('planBook.freeGuide')}
            </button>
          </div>
        </div>
      </section>
    </div>
  );
};

export default PlanBook;
