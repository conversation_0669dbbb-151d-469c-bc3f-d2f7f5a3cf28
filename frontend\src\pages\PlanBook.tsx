import React, { useState } from 'react';
import { useTranslation } from 'react-i18next';
import { Link } from 'react-router-dom';
import {
  CreditCard,
  Clock,
  FileCheck,
  Shield,
  Hotel,
  Car,
  Route,
  Package,
  Heart,
  DollarSign
} from 'lucide-react';

interface TabContent {
  id: string;
  title: string;
  subtitle: string;
  items: Array<{
    icon: React.ReactNode;
    title: string;
    description: string;
    image: string;
    link: string;
  }>;
}

const PlanBook: React.FC = () => {
  const { t } = useTranslation();
  const [activeTab, setActiveTab] = useState('planJourney');

  const tabs = [
    {
      id: 'planJourney',
      label: t('planBook.planJourney.title'),
      shortLabel: t('planBook.planJourney.shortLabel')
    },
    {
      id: 'book',
      label: t('planBook.book.title'),
      shortLabel: t('planBook.book.shortLabel')
    },
    {
      id: 'beforeFly',
      label: t('planBook.beforeFly.title'),
      shortLabel: t('planBook.beforeFly.shortLabel')
    }
  ];

  const tabContent: Record<string, TabContent> = {
    planJourney: {
      id: 'planJourney',
      title: t('planBook.planJourney.title'),
      subtitle: t('planBook.planJourney.subtitle'),
      items: [
        {
          icon: <DollarSign className="h-8 w-8" />,
          title: t('planBook.planJourney.priceTypes'),
          description: t('planBook.planJourney.priceTypesDesc'),
          image: 'https://images.unsplash.com/photo-1556742049-0cfed4f6a45d?ixlib=rb-4.0.3&auto=format&fit=crop&w=2340&q=80',
          link: '/price-types'
        },
        {
          icon: <CreditCard className="h-8 w-8" />,
          title: t('planBook.planJourney.paymentMethods'),
          description: t('planBook.planJourney.paymentMethodsDesc'),
          image: 'https://images.unsplash.com/photo-1563013544-824ae1b704d3?ixlib=rb-4.0.3&auto=format&fit=crop&w=2340&q=80',
          link: '/payment-methods'
        },
        {
          icon: <Clock className="h-8 w-8" />,
          title: t('planBook.planJourney.lowPrices'),
          description: t('planBook.planJourney.lowPricesDesc'),
          image: 'https://images.unsplash.com/photo-1551836022-deb4988cc6c0?ixlib=rb-4.0.3&auto=format&fit=crop&w=2340&q=80',
          link: '/low-prices'
        }
      ]
    },
    book: {
      id: 'book',
      title: t('planBook.book.title'),
      subtitle: t('planBook.book.subtitle'),
      items: [
        {
          icon: <Hotel className="h-8 w-8" />,
          title: t('planBook.book.hotels'),
          description: t('planBook.book.hotelsDesc'),
          image: 'https://images.unsplash.com/photo-1566073771259-6a8506099945?ixlib=rb-4.0.3&auto=format&fit=crop&w=2340&q=80',
          link: '/hotels'
        },
        {
          icon: <Car className="h-8 w-8" />,
          title: t('planBook.book.cars'),
          description: t('planBook.book.carsDesc'),
          image: 'https://images.unsplash.com/photo-1549924231-f129b911e442?ixlib=rb-4.0.3&auto=format&fit=crop&w=2340&q=80',
          link: '/car-rental'
        },
        {
          icon: <Route className="h-8 w-8" />,
          title: t('planBook.book.transfers'),
          description: t('planBook.book.transfersDesc'),
          image: 'https://images.unsplash.com/photo-1449824913935-59a10b8d2000?ixlib=rb-4.0.3&auto=format&fit=crop&w=2340&q=80',
          link: '/airport-transfers'
        },
        {
          icon: <Package className="h-8 w-8" />,
          title: t('planBook.book.shipping'),
          description: t('planBook.book.shippingDesc'),
          image: 'https://images.unsplash.com/photo-1578662996442-48f60103fc96?ixlib=rb-4.0.3&auto=format&fit=crop&w=2340&q=80',
          link: '/shipping'
        }
      ]
    },
    beforeFly: {
      id: 'beforeFly',
      title: t('planBook.beforeFly.title'),
      subtitle: t('planBook.beforeFly.subtitle'),
      items: [
        {
          icon: <FileCheck className="h-8 w-8" />,
          title: t('planBook.beforeFly.checkin'),
          description: t('planBook.beforeFly.checkinDesc'),
          image: 'https://images.unsplash.com/photo-1436491865332-7a61a109cc05?ixlib=rb-4.0.3&auto=format&fit=crop&w=2340&q=80',
          link: '/checkin-info'
        },
        {
          icon: <Shield className="h-8 w-8" />,
          title: t('planBook.beforeFly.visas'),
          description: t('planBook.beforeFly.visasDesc'),
          image: 'https://images.unsplash.com/photo-1554224155-6726b3ff858f?ixlib=rb-4.0.3&auto=format&fit=crop&w=2340&q=80',
          link: '/visa-info'
        },
        {
          icon: <Heart className="h-8 w-8" />,
          title: t('planBook.beforeFly.vaccinations'),
          description: t('planBook.beforeFly.vaccinationsDesc'),
          image: 'https://images.unsplash.com/photo-1559757148-5c350d0d3c56?ixlib=rb-4.0.3&auto=format&fit=crop&w=2340&q=80',
          link: '/vaccinations'
        }
      ]
    }
  };

  const currentContent = tabContent[activeTab];

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Hero Section */}
      <section className="hero relative min-h-[60vh] flex items-center overflow-hidden">
        {/* Background */}
        <div className="hero-bg"></div>
        <div className="hero-overlay"></div>
        <div className="absolute inset-0">
          <img
            src="https://images.unsplash.com/photo-1488646953014-85cb44e25828?ixlib=rb-4.0.3&auto=format&fit=crop&w=2070&q=80"
            alt="Plan and Book - Travel Planning"
            className="img-cover opacity-40"
            loading="eager"
          />
        </div>

        <div className="container relative z-10 py-20">
          <div className="hero-content">
            <div className="mb-6 animate-fade-in">
              <div className="inline-flex items-center px-6 py-3 bg-white/10 backdrop-blur-sm rounded-full text-white/90 text-sm font-medium mb-6 border border-white/20">
                <DollarSign className="w-5 h-5 mr-2 rtl:ml-2 rtl:mr-0" />
                {t('planBook.title')}
              </div>
            </div>
            <h1 className="text-5xl md:text-7xl font-display font-bold mb-8 leading-tight text-white animate-fade-in">
              {t('planBook.title')}
            </h1>
            <p className="text-xl md:text-2xl text-white/90 max-w-4xl mx-auto mb-12 animate-slide-up font-body leading-relaxed">
              {t('planBook.subtitle')}
            </p>

            {/* Quick Stats */}
            <div className="flex flex-wrap items-center justify-center gap-8 text-white/80 text-sm animate-slide-up">
              <div className="flex items-center bg-white/10 backdrop-blur-sm rounded-full px-4 py-2">
                <Hotel className="w-4 h-4 mr-2 rtl:ml-2 rtl:mr-0" />
                <span>+10,000 فندق</span>
              </div>
              <div className="flex items-center bg-white/10 backdrop-blur-sm rounded-full px-4 py-2">
                <Car className="w-4 h-4 mr-2 rtl:ml-2 rtl:mr-0" />
                <span>تأجير سيارات</span>
              </div>
              <div className="flex items-center bg-white/10 backdrop-blur-sm rounded-full px-4 py-2">
                <Shield className="w-4 h-4 mr-2 rtl:ml-2 rtl:mr-0" />
                <span>حجز آمن</span>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Navigation Tabs */}
      <section className="bg-white/95 backdrop-blur-lg shadow-medium sticky top-0 z-40 border-b border-gray-100">
        <div className="container">
          <nav
            className="flex justify-center space-x-2 rtl:space-x-reverse overflow-x-auto py-6"
            role="tablist"
            aria-label={t('planBook.title')}
          >
            {tabs.map((tab, index) => (
              <button
                key={tab.id}
                onClick={() => setActiveTab(tab.id)}
                role="tab"
                aria-selected={activeTab === tab.id}
                aria-controls={`tabpanel-${tab.id}`}
                className={`relative px-8 py-4 text-sm font-semibold rounded-full whitespace-nowrap transition-all duration-300 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2 transform hover:scale-105 ${
                  activeTab === tab.id
                    ? 'bg-gradient-to-r from-primary-500 to-secondary-500 text-white shadow-lg'
                    : 'text-gray-600 hover:text-primary-600 hover:bg-primary-50 border border-gray-200 hover:border-primary-200'
                }`}
              >
                {/* Active indicator */}
                {activeTab === tab.id && (
                  <div className="absolute inset-0 bg-gradient-to-r from-primary-600 to-secondary-600 rounded-full opacity-90 -z-10"></div>
                )}

                <span className="hidden md:inline relative z-10">{tab.label}</span>
                <span className="md:hidden relative z-10">{tab.shortLabel}</span>

                {/* Tab number indicator */}
                <span className={`absolute -top-2 -right-2 w-6 h-6 rounded-full text-xs flex items-center justify-center font-bold transition-all duration-300 ${
                  activeTab === tab.id
                    ? 'bg-white text-primary-600 shadow-md'
                    : 'bg-gray-200 text-gray-500'
                }`}>
                  {index + 1}
                </span>
              </button>
            ))}
          </nav>
        </div>
      </section>

      {/* Content Section */}
      <section className="section bg-gray-50">
        <div className="container">
          <div
            id={`tabpanel-${activeTab}`}
            role="tabpanel"
            aria-labelledby={`tab-${activeTab}`}
            className="animate-fade-in"
          >
            {/* Section Header */}
            <div className="text-center mb-16">
              <div className="inline-flex items-center px-4 py-2 bg-primary-50 text-primary-600 rounded-full text-sm font-medium mb-4">
                <span className="w-2 h-2 bg-primary-500 rounded-full mr-2 rtl:ml-2 rtl:mr-0"></span>
                {currentContent.title}
              </div>
              <h2 className="text-4xl md:text-5xl font-display font-bold text-gray-900 mb-6 text-gradient">
                {currentContent.title}
              </h2>
              <p className="text-xl text-gray-600 max-w-4xl mx-auto leading-relaxed">
                {currentContent.subtitle}
              </p>
            </div>

            {/* Cards Grid */}
            <div className="grid grid-cols-1 md:grid-cols-2 xl:grid-cols-3 gap-8 lg:gap-10">
              {currentContent.items.map((item, index) => (
                <article
                  key={index}
                  className="card group cursor-pointer transform hover:scale-105 transition-all duration-500 animate-slide-up"
                  style={{ animationDelay: `${index * 0.1}s` }}
                >
                  <div className="relative overflow-hidden">
                    <img
                      src={item.image}
                      alt={item.title}
                      className="w-full h-64 object-cover group-hover:scale-110 transition-transform duration-700"
                      loading="lazy"
                    />
                    <div className="absolute inset-0 bg-gradient-to-t from-black/80 via-black/20 to-transparent"></div>

                    {/* Icon Badge */}
                    <div className="absolute top-4 right-4 rtl:left-4 rtl:right-auto">
                      <div className="w-14 h-14 bg-white/10 backdrop-blur-sm rounded-xl flex items-center justify-center border border-white/20 group-hover:bg-primary-500 group-hover:border-primary-400 transition-all duration-300">
                        <div className="text-white group-hover:scale-110 transition-transform duration-300">
                          {item.icon}
                        </div>
                      </div>
                    </div>

                    {/* Card Number */}
                    <div className="absolute top-4 left-4 rtl:right-4 rtl:left-auto">
                      <div className="w-8 h-8 bg-primary-500 text-white rounded-full flex items-center justify-center text-sm font-bold shadow-lg">
                        {index + 1}
                      </div>
                    </div>

                    {/* Overlay Content */}
                    <div className="absolute bottom-0 left-0 right-0 p-6 text-white">
                      <h3 className="text-xl font-bold mb-2 group-hover:text-primary-200 transition-colors duration-300">
                        {item.title}
                      </h3>
                    </div>
                  </div>

                  <div className="card-body">
                    <p className="text-gray-600 mb-6 leading-relaxed">
                      {item.description}
                    </p>

                    <Link
                      to={item.link}
                      className="btn btn-primary w-full group-hover:from-primary-600 group-hover:to-secondary-600"
                      aria-label={`${t('planBook.learnMore')} - ${item.title}`}
                    >
                      <span>{t('planBook.learnMore')}</span>
                      <svg className="w-4 h-4 mr-2 rtl:ml-2 rtl:mr-0 group-hover:translate-x-1 rtl:group-hover:-translate-x-1 transition-transform duration-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                      </svg>
                    </Link>
                  </div>
                </article>
              ))}
            </div>
          </div>
        </div>
      </section>

      {/* Features & Stats Section */}
      <section className="section bg-white">
        <div className="container">
          <div className="text-center mb-16">
            <div className="inline-flex items-center px-4 py-2 bg-secondary-50 text-secondary-600 rounded-full text-sm font-medium mb-4">
              <Package className="w-4 h-4 mr-2 rtl:ml-2 rtl:mr-0" />
              لماذا تختار SkyWays؟
            </div>
            <h2 className="text-4xl md:text-5xl font-display font-bold text-gray-900 mb-6">
              تجربة سفر <span className="text-gradient-accent">استثنائية</span>
            </h2>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto">
              نقدم لك أفضل الخدمات وأكثرها تطوراً لضمان رحلة مريحة وممتعة
            </p>
          </div>

          {/* Stats Grid */}
          <div className="grid grid-cols-2 md:grid-cols-4 gap-8 mb-16">
            <div className="text-center">
              <div className="w-16 h-16 bg-gradient-to-r from-primary-500 to-secondary-500 rounded-full flex items-center justify-center mx-auto mb-4 shadow-lg">
                <Hotel className="w-8 h-8 text-white" />
              </div>
              <div className="text-3xl font-bold text-gray-900 mb-2">+10K</div>
              <div className="text-gray-600 text-sm">فندق شريك</div>
            </div>
            <div className="text-center">
              <div className="w-16 h-16 bg-gradient-to-r from-secondary-500 to-accent-500 rounded-full flex items-center justify-center mx-auto mb-4 shadow-lg">
                <Car className="w-8 h-8 text-white" />
              </div>
              <div className="text-3xl font-bold text-gray-900 mb-2">500+</div>
              <div className="text-gray-600 text-sm">شركة تأجير</div>
            </div>
            <div className="text-center">
              <div className="w-16 h-16 bg-gradient-to-r from-accent-500 to-primary-500 rounded-full flex items-center justify-center mx-auto mb-4 shadow-lg">
                <Shield className="w-8 h-8 text-white" />
              </div>
              <div className="text-3xl font-bold text-gray-900 mb-2">100%</div>
              <div className="text-gray-600 text-sm">حجز آمن</div>
            </div>
            <div className="text-center">
              <div className="w-16 h-16 bg-gradient-to-r from-primary-500 to-accent-500 rounded-full flex items-center justify-center mx-auto mb-4 shadow-lg">
                <Heart className="w-8 h-8 text-white" />
              </div>
              <div className="text-3xl font-bold text-gray-900 mb-2">4.9</div>
              <div className="text-gray-600 text-sm">تقييم العملاء</div>
            </div>
          </div>

          {/* Features Grid */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            <div className="text-center p-6 rounded-2xl bg-gradient-to-br from-primary-50 to-secondary-50 border border-primary-100 hover:shadow-lg transition-all duration-300">
              <div className="w-12 h-12 bg-primary-500 rounded-xl flex items-center justify-center mx-auto mb-4">
                <Clock className="w-6 h-6 text-white" />
              </div>
              <h3 className="text-lg font-bold text-gray-900 mb-2">حجز فوري</h3>
              <p className="text-gray-600 text-sm">تأكيد الحجز خلال ثوانٍ</p>
            </div>
            <div className="text-center p-6 rounded-2xl bg-gradient-to-br from-secondary-50 to-accent-50 border border-secondary-100 hover:shadow-lg transition-all duration-300">
              <div className="w-12 h-12 bg-secondary-500 rounded-xl flex items-center justify-center mx-auto mb-4">
                <CreditCard className="w-6 h-6 text-white" />
              </div>
              <h3 className="text-lg font-bold text-gray-900 mb-2">دفع آمن</h3>
              <p className="text-gray-600 text-sm">طرق دفع متعددة ومؤمنة</p>
            </div>
            <div className="text-center p-6 rounded-2xl bg-gradient-to-br from-accent-50 to-primary-50 border border-accent-100 hover:shadow-lg transition-all duration-300">
              <div className="w-12 h-12 bg-accent-500 rounded-xl flex items-center justify-center mx-auto mb-4">
                <Heart className="w-6 h-6 text-white" />
              </div>
              <h3 className="text-lg font-bold text-gray-900 mb-2">دعم 24/7</h3>
              <p className="text-gray-600 text-sm">فريق دعم متاح دائماً</p>
            </div>
          </div>
        </div>
      </section>

      {/* Call to Action */}
      <section className="hero relative overflow-hidden">
        {/* Background */}
        <div className="hero-bg"></div>
        <div className="hero-overlay"></div>
        <div className="absolute inset-0">
          <img
            src="https://images.unsplash.com/photo-1556388158-158ea5ccacbd?ixlib=rb-4.0.3&auto=format&fit=crop&w=2070&q=80"
            alt="Travel Expert Support"
            className="img-cover opacity-30"
          />
        </div>

        <div className="container relative z-10 section text-center">
          <div className="max-w-4xl mx-auto">
            {/* Badge */}
            <div className="inline-flex items-center px-6 py-3 bg-white/10 backdrop-blur-sm rounded-full text-white/90 text-sm font-medium mb-6 border border-white/20">
              <Heart className="w-5 h-5 mr-2 rtl:ml-2 rtl:mr-0" />
              دعم العملاء 24/7
            </div>

            <h2 className="text-4xl md:text-6xl font-display font-bold mb-6 text-white leading-tight">
              {t('planBook.helpTitle')}
            </h2>
            <p className="text-xl md:text-2xl text-white/90 mb-12 leading-relaxed max-w-3xl mx-auto">
              {t('planBook.helpSubtitle')}
            </p>

            {/* Action Buttons */}
            <div className="flex flex-col sm:flex-row gap-6 justify-center items-center">
              <button
                className="btn bg-white text-primary-600 hover:bg-gray-100 font-bold px-10 py-4 text-lg shadow-xl hover:shadow-2xl border-0 min-w-[200px]"
                aria-label={t('planBook.talkToExpert')}
              >
                <Heart className="w-5 h-5 mr-2 rtl:ml-2 rtl:mr-0" />
                {t('planBook.talkToExpert')}
              </button>
              <button
                className="btn border-2 border-white/30 text-white hover:bg-white hover:text-primary-700 font-bold px-10 py-4 text-lg backdrop-blur-sm min-w-[200px]"
                aria-label={t('planBook.freeGuide')}
              >
                <FileCheck className="w-5 h-5 mr-2 rtl:ml-2 rtl:mr-0" />
                {t('planBook.freeGuide')}
              </button>
            </div>

            {/* Trust Indicators */}
            <div className="mt-16 grid grid-cols-1 md:grid-cols-3 gap-8 text-white/80">
              <div className="flex flex-col items-center">
                <div className="w-16 h-16 bg-white/10 backdrop-blur-sm rounded-full flex items-center justify-center mb-4 border border-white/20">
                  <Shield className="w-8 h-8" />
                </div>
                <h3 className="font-semibold mb-2">حجز آمن 100%</h3>
                <p className="text-sm text-white/70">حماية كاملة لبياناتك</p>
              </div>
              <div className="flex flex-col items-center">
                <div className="w-16 h-16 bg-white/10 backdrop-blur-sm rounded-full flex items-center justify-center mb-4 border border-white/20">
                  <Clock className="w-8 h-8" />
                </div>
                <h3 className="font-semibold mb-2">دعم فوري</h3>
                <p className="text-sm text-white/70">استجابة خلال دقائق</p>
              </div>
              <div className="flex flex-col items-center">
                <div className="w-16 h-16 bg-white/10 backdrop-blur-sm rounded-full flex items-center justify-center mb-4 border border-white/20">
                  <Heart className="w-8 h-8" />
                </div>
                <h3 className="font-semibold mb-2">رضا العملاء</h3>
                <p className="text-sm text-white/70">تقييم 4.9/5 نجوم</p>
              </div>
            </div>
          </div>
        </div>
      </section>
    </div>
  );
};

export default PlanBook;
