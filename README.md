# FlyWay - طرق السماء ✈️

موقع حجز تذاكر الطيران الشامل مع خدمات السفر المتكاملة

## 📋 نظرة عامة

FlyWay هو نظام حجز طيران متكامل يوفر:
- حجز تذاكر الطيران لوجهات متعددة
- خدمات إضافية (فنادق، سيارات، نقل مطارات)
- برنامج ولاء متقدم (nasmiles)
- واجهة مستخدم متعددة اللغات (عربي/إنجليزي)

## 🏗️ هيكل المشروع

```
SkyWays/
├── backend/                 # Node.js + Express API
│   ├── config/             # إعدادات قاعدة البيانات
│   ├── controllers/        # منطق العمليات
│   ├── models/             # نماذج MongoDB
│   ├── routes/             # مسارات API
│   ├── middlewares/        # وسائط المصادقة والأمان
│   ├── utils/              # أدوات مساعدة
│   └── app.js              # ملف التطبيق الرئيسي
├── frontend/               # React + TypeScript + Tailwind
│   ├── src/
│   │   ├── components/     # مكونات React
│   │   ├── pages/          # صفحات التطبيق
│   │   ├── hooks/          # React Hooks مخصصة
│   │   ├── services/       # خدمات API
│   │   ├── types/          # تعريفات TypeScript
│   │   └── utils/          # أدوات مساعدة
│   └── public/             # ملفات عامة
└── README.md
```

## 🚀 التقنيات المستخدمة

### Backend
- **Node.js** - بيئة تشغيل JavaScript
- **Express.js** - إطار عمل الويب
- **MongoDB** - قاعدة البيانات
- **Mongoose** - ODM لـ MongoDB
- **JWT** - المصادقة والتوكن
- **bcryptjs** - تشفير كلمات المرور

### Frontend
- **React 18** - مكتبة واجهة المستخدم
- **TypeScript** - JavaScript مع الأنواع
- **Tailwind CSS** - إطار عمل CSS
- **Vite** - أداة البناء
- **React Router** - التنقل
- **React Query** - إدارة البيانات
- **React Hook Form** - إدارة النماذج

## 📦 التثبيت والتشغيل

### المتطلبات الأساسية
- Node.js (v18 أو أحدث)
- MongoDB (محلي أو سحابي)
- npm أو yarn

### 1. تثبيت Backend

```bash
# الانتقال إلى مجلد Backend
cd backend

# تثبيت التبعيات
npm install

# إنشاء ملف البيئة
cp .env.example .env

# تحديث متغيرات البيئة في .env
# MONGODB_URI=mongodb://localhost:27017/flyway_db
# JWT_SECRET=your_jwt_secret_here

# تشغيل البيانات التجريبية
npm run seed

# تشغيل الخادم في وضع التطوير
npm run dev
```

### 2. تثبيت Frontend

```bash
# الانتقال إلى مجلد Frontend
cd frontend

# تثبيت التبعيات
npm install

# تشغيل التطبيق في وضع التطوير
npm run dev
```

## 🗄️ قاعدة البيانات

### النماذج الرئيسية

1. **User** - بيانات المستخدمين
2. **Flight** - معلومات الرحلات
3. **Booking** - الحجوزات
4. **Destination** - الوجهات
5. **LoyaltyAccount** - حسابات الولاء
6. **Offer** - العروض والخصومات
7. **Hotel** - الفنادق

### إضافة البيانات التجريبية

```bash
cd backend
node utils/seedData.js
```

## 🔗 API Endpoints

### المصادقة
- `POST /api/auth/register` - تسجيل مستخدم جديد
- `POST /api/auth/login` - تسجيل الدخول
- `GET /api/auth/me` - الحصول على بيانات المستخدم الحالي
- `PUT /api/auth/profile` - تحديث الملف الشخصي
- `PUT /api/auth/change-password` - تغيير كلمة المرور

### الرحلات (قريباً)
- `POST /api/flights/search` - البحث عن رحلات
- `GET /api/flights/:id` - تفاصيل رحلة محددة

### الحجوزات (قريباً)
- `POST /api/bookings` - إنشاء حجز جديد
- `GET /api/bookings` - قائمة حجوزات المستخدم
- `GET /api/bookings/:id` - تفاصيل حجز محدد

## 🌐 الواجهات

### الواجهة الرئيسية
- نموذج البحث عن الرحلات
- عرض العروض المميزة
- الوجهات الشائعة

### الواجهات الأربع الرئيسية

1. **Plan and Book** - التخطيط والحجز
   - Plan Your Journey
   - Book
   - Before You Fly

2. **Prepare Your Trip** - الاستعداد للرحلة
   - معلومات الأمتعة
   - إدارة الحجز
   - خدمات تعزيز الرحلة

3. **Information** - المعلومات
   - معلومات الشركة
   - المساعدة والدعم
   - الخدمات الخاصة

4. **nasmiles** - برنامج الولاء
   - التسجيل وكسب النقاط
   - استبدال النقاط
   - مزايا العضوية

## 🔒 الأمان

- تشفير كلمات المرور باستخدام bcrypt
- مصادقة JWT
- حماية من CORS
- تحديد معدل الطلبات (Rate Limiting)
- تنظيف البيانات المدخلة

## 🌍 دعم اللغات

- العربية (افتراضي)
- الإنجليزية

## 📱 التصميم المتجاوب

- Mobile First Design
- دعم جميع أحجام الشاشات
- واجهة مستخدم حديثة ومتجاوبة

## 🚧 المراحل القادمة

### المرحلة الثانية
- [ ] إكمال APIs الرحلات والحجوزات
- [ ] إضافة APIs الوجهات والعروض
- [ ] تطوير واجهة المستخدم الأساسية

### المرحلة الثالثة
- [ ] تطوير الواجهات الأربع الرئيسية
- [ ] تطبيق برنامج الولاء
- [ ] إضافة خدمات الفنادق والسيارات

### المرحلة الرابعة
- [ ] تحسين الأداء
- [ ] إضافة الاختبارات
- [ ] التوثيق الشامل
- [ ] النشر والإنتاج

## 🤝 المساهمة

نرحب بالمساهمات! يرجى قراءة دليل المساهمة قبل البدء.

## 📄 الترخيص

هذا المشروع مرخص تحت رخصة MIT.

---

**FlyWay Team** - طرق السماء ✈️
