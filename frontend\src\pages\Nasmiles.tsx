import React, { useState } from 'react';
import { useTranslation } from 'react-i18next';
import { 
  StarIcon,
  GiftIcon,
  CreditCardIcon,
  UserGroupIcon,
  TrophyIcon,
  CheckCircleIcon,
  ArrowUpIcon
} from '@heroicons/react/24/outline';

const Nasmiles: React.FC = () => {
  const { t } = useTranslation();
  const [activeTab, setActiveTab] = useState('overview');

  const tabs = [
    { id: 'overview', label: 'نظرة عامة', shortLabel: 'نظرة عامة' },
    { id: 'tiers', label: 'المستويات', shortLabel: 'المستويات' },
    { id: 'earn', label: 'اكسب النقاط', shortLabel: 'اكسب' },
    { id: 'redeem', label: 'استبدل النقاط', shortLabel: 'استبدل' },
    { id: 'partners', label: 'الشركاء', shortLabel: 'الشركاء' }
  ];

  const tiers = [
    {
      name: t('nasmiles.tiers.blue'),
      nameAr: 'أزرق',
      color: 'blue',
      bgGradient: 'from-blue-400 to-blue-600',
      requirements: '0 - 24,999 ميل',
      benefits: [
        'كسب الميلات الأساسية',
        'عروض خاصة للأعضاء',
        'حجز المقاعد مسبقاً (مجاني)',
        'دعم العملاء المخصص'
      ],
      icon: '🔷'
    },
    {
      name: t('nasmiles.tiers.silver'),
      nameAr: 'فضي',
      color: 'gray',
      bgGradient: 'from-gray-400 to-gray-600',
      requirements: '25,000 - 49,999 ميل',
      benefits: [
        'جميع مزايا المستوى الأزرق',
        'كسب ميلات إضافية 25%',
        'ترقية مجانية (حسب التوفر)',
        'أولوية في قائمة الانتظار',
        'أمتعة إضافية مجانية'
      ],
      icon: '🥈'
    },
    {
      name: t('nasmiles.tiers.gold'),
      nameAr: 'ذهبي',
      color: 'yellow',
      bgGradient: 'from-yellow-400 to-yellow-600',
      requirements: '50,000 - 99,999 ميل',
      benefits: [
        'جميع مزايا المستوى الفضي',
        'كسب ميلات إضافية 50%',
        'ترقية درجة الأعمال مجاناً',
        'الوصول لصالات VIP',
        'تسجيل دخول سريع',
        'أمتعة إضافية 40 كيلو'
      ],
      icon: '🥇'
    },
    {
      name: t('nasmiles.tiers.platinum'),
      nameAr: 'بلاتيني',
      color: 'purple',
      bgGradient: 'from-purple-400 to-purple-600',
      requirements: '100,000+ ميل',
      benefits: [
        'جميع مزايا المستوى الذهبي',
        'كسب ميلات إضافية 100%',
        'ترقية الدرجة الأولى مجاناً',
        'صالات VIP حصرية',
        'مساعد شخصي للسفر',
        'مقاعد محجوزة دائماً',
        'أمتعة إضافية غير محدودة'
      ],
      icon: '💎'
    }
  ];

  const earningMethods = [
    {
      icon: <StarIcon className="h-8 w-8" />,
      title: 'الطيران مع SkyWays',
      description: 'اكسب ميلات مع كل رحلة تحجزها',
      points: '5-10 ميلات لكل ريال'
    },
    {
      icon: <CreditCardIcon className="h-8 w-8" />,
      title: 'بطاقة ناسميلز الائتمانية',
      description: 'اكسب ميلات مع كل عملية شراء',
      points: '2 ميل لكل ريال'
    },
    {
      icon: <UserGroupIcon className="h-8 w-8" />,
      title: 'الشركاء المحليين',
      description: 'اكسب ميلات من فنادق ومطاعم وتسوق',
      points: '1-3 ميلات لكل ريال'
    },
    {
      icon: <GiftIcon className="h-8 w-8" />,
      title: 'العروض الخاصة',
      description: 'مضاعفة الميلات في المناسبات',
      points: 'حتى 20 ميل لكل ريال'
    }
  ];

  const redeemOptions = [
    {
      icon: '✈️',
      title: 'تذاكر طيران مجانية',
      description: 'استبدل ميلاتك برحلات مجانية',
      startingFrom: 'من 15,000 ميل'
    },
    {
      icon: '⬆️',
      title: 'ترقية الدرجة',
      description: 'ترقية إلى درجة الأعمال أو الأولى',
      startingFrom: 'من 8,000 ميل'
    },
    {
      icon: '🏨',
      title: 'إقامة في الفنادق',
      description: 'ليالي مجانية في أفضل الفنادق',
      startingFrom: 'من 5,000 ميل'
    },
    {
      icon: '🎁',
      title: 'منتجات وخدمات',
      description: 'مجموعة متنوعة من المنتجات والخدمات',
      startingFrom: 'من 1,000 ميل'
    }
  ];

  const partners = [
    { name: 'هيلتون', category: 'فنادق', logo: '🏨' },
    { name: 'أفيس', category: 'تأجير سيارات', logo: '🚗' },
    { name: 'أكسترا', category: 'تسوق', logo: '🛍️' },
    { name: 'ستاربكس', category: 'مطاعم', logo: '☕' },
    { name: 'سامسونج', category: 'إلكترونيات', logo: '📱' },
    { name: 'أديداس', category: 'رياضة', logo: '👟' }
  ];

  const renderOverview = () => (
    <div className="space-y-16">
      {/* Benefits Overview */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
        <div className="text-center p-6 bg-white rounded-xl shadow-lg">
          <div className="w-16 h-16 bg-gradient-to-br from-blue-500 to-purple-600 rounded-full flex items-center justify-center mx-auto mb-4">
            <StarIcon className="h-8 w-8 text-white" />
          </div>
          <h3 className="text-xl font-bold mb-2">اكسب ميلات</h3>
          <p className="text-gray-600">اكسب ميلات مع كل رحلة وعملية شراء من الشركاء</p>
        </div>

        <div className="text-center p-6 bg-white rounded-xl shadow-lg">
          <div className="w-16 h-16 bg-gradient-to-br from-green-500 to-teal-600 rounded-full flex items-center justify-center mx-auto mb-4">
            <GiftIcon className="h-8 w-8 text-white" />
          </div>
          <h3 className="text-xl font-bold mb-2">استبدل المكافآت</h3>
          <p className="text-gray-600">استبدل ميلاتك برحلات مجانية وترقيات وهدايا</p>
        </div>

        <div className="text-center p-6 bg-white rounded-xl shadow-lg">
          <div className="w-16 h-16 bg-gradient-to-br from-yellow-500 to-orange-600 rounded-full flex items-center justify-center mx-auto mb-4">
            <TrophyIcon className="h-8 w-8 text-white" />
          </div>
          <h3 className="text-xl font-bold mb-2">مزايا حصرية</h3>
          <p className="text-gray-600">استمتع بمزايا حصرية كلما ارتقيت في المستويات</p>
        </div>
      </div>

      {/* Join CTA */}
      <div className="bg-gradient-to-r from-blue-600 to-purple-700 text-white rounded-2xl p-8 text-center">
        <h2 className="text-3xl font-bold mb-4">انضم إلى ناسميلز اليوم</h2>
        <p className="text-xl mb-6 text-blue-100">ابدأ رحلة الكسب والاستفادة من المزايا الحصرية</p>
        <button className="bg-white text-blue-600 px-8 py-3 rounded-lg font-semibold hover:bg-gray-100 transition-colors duration-200 text-lg">
          انضم مجاناً الآن
        </button>
      </div>
    </div>
  );

  const renderTiers = () => (
    <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
      {tiers.map((tier, index) => (
        <div key={tier.name} className="bg-white rounded-2xl shadow-lg overflow-hidden">
          <div className={`bg-gradient-to-r ${tier.bgGradient} p-6 text-white`}>
            <div className="flex items-center justify-between mb-4">
              <div>
                <h3 className="text-2xl font-bold">{tier.nameAr}</h3>
                <p className="text-sm opacity-90">{tier.requirements}</p>
              </div>
              <div className="text-4xl">{tier.icon}</div>
            </div>
          </div>
          
          <div className="p-6">
            <h4 className="text-lg font-semibold mb-4">المزايا:</h4>
            <ul className="space-y-2">
              {tier.benefits.map((benefit, idx) => (
                <li key={idx} className="flex items-start">
                  <CheckCircleIcon className="h-5 w-5 text-green-500 mr-2 rtl:ml-2 rtl:mr-0 mt-0.5 flex-shrink-0" />
                  <span className="text-gray-700">{benefit}</span>
                </li>
              ))}
            </ul>
            
            {index < tiers.length - 1 && (
              <div className="mt-6 pt-4 border-t">
                <div className="flex items-center text-blue-600">
                  <ArrowUpIcon className="h-4 w-4 mr-1 rtl:ml-1 rtl:mr-0" />
                  <span className="text-sm font-medium">
                    المستوى التالي: {tiers[index + 1].nameAr}
                  </span>
                </div>
              </div>
            )}
          </div>
        </div>
      ))}
    </div>
  );

  const renderEarn = () => (
    <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
      {earningMethods.map((method, index) => (
        <div key={index} className="bg-white rounded-xl shadow-lg p-6 hover:shadow-xl transition-shadow duration-300">
          <div className="flex items-start mb-4">
            <div className="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center mr-4 rtl:ml-4 rtl:mr-0">
              <div className="text-blue-600">{method.icon}</div>
            </div>
            <div className="flex-1">
              <h3 className="text-xl font-bold mb-2">{method.title}</h3>
              <p className="text-gray-600 mb-3">{method.description}</p>
              <div className="bg-gradient-to-r from-blue-50 to-purple-50 p-3 rounded-lg">
                <span className="text-blue-600 font-semibold">{method.points}</span>
              </div>
            </div>
          </div>
        </div>
      ))}
    </div>
  );

  const renderRedeem = () => (
    <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
      {redeemOptions.map((option, index) => (
        <div key={index} className="bg-white rounded-xl shadow-lg overflow-hidden hover:shadow-xl transition-shadow duration-300">
          <div className="p-6">
            <div className="text-center mb-4">
              <div className="text-4xl mb-2">{option.icon}</div>
              <h3 className="text-xl font-bold">{option.title}</h3>
            </div>
            <p className="text-gray-600 text-center mb-4">{option.description}</p>
            <div className="bg-gradient-to-r from-green-50 to-teal-50 p-4 rounded-lg text-center">
              <span className="text-green-600 font-semibold">{option.startingFrom}</span>
            </div>
            <button className="w-full mt-4 bg-blue-600 text-white py-2 rounded-lg hover:bg-blue-700 transition-colors duration-200">
              استبدل الآن
            </button>
          </div>
        </div>
      ))}
    </div>
  );

  const renderPartners = () => (
    <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-6">
      {partners.map((partner, index) => (
        <div key={index} className="bg-white rounded-xl shadow-lg p-6 text-center hover:shadow-xl transition-shadow duration-300">
          <div className="text-4xl mb-3">{partner.logo}</div>
          <h3 className="font-semibold mb-1">{partner.name}</h3>
          <p className="text-sm text-gray-600">{partner.category}</p>
        </div>
      ))}
    </div>
  );

  const renderContent = () => {
    switch (activeTab) {
      case 'overview': return renderOverview();
      case 'tiers': return renderTiers();
      case 'earn': return renderEarn();
      case 'redeem': return renderRedeem();
      case 'partners': return renderPartners();
      default: return renderOverview();
    }
  };

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Hero Section */}
      <section className="bg-gradient-to-r from-blue-600 via-purple-600 to-indigo-700 text-white py-20">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center">
            <div className="flex items-center justify-center mb-6">
              <StarIcon className="h-12 w-12 text-yellow-300 mr-3 rtl:ml-3 rtl:mr-0" />
              <h1 className="text-5xl md:text-6xl font-bold">
                {t('nasmiles.title')}
              </h1>
            </div>
            <p className="text-xl md:text-2xl text-blue-100 max-w-3xl mx-auto">
              {t('nasmiles.subtitle')}
            </p>
            <div className="mt-8">
              <button className="bg-white text-blue-600 px-8 py-4 rounded-lg font-bold text-lg hover:bg-gray-100 transition-colors duration-200 mr-4 rtl:ml-4 rtl:mr-0">
                {t('nasmiles.joinNow')}
              </button>
              <button className="border-2 border-white text-white px-8 py-4 rounded-lg font-bold text-lg hover:bg-white hover:text-blue-600 transition-colors duration-200">
                تسجيل الدخول
              </button>
            </div>
          </div>
        </div>
      </section>

      {/* Navigation Tabs */}
      <section className="bg-white shadow-sm sticky top-0 z-40">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex space-x-8 rtl:space-x-reverse overflow-x-auto">
            {tabs.map((tab) => (
              <button
                key={tab.id}
                onClick={() => setActiveTab(tab.id)}
                className={`py-4 px-2 text-sm font-medium border-b-2 whitespace-nowrap transition-colors duration-200 ${
                  activeTab === tab.id
                    ? 'border-blue-600 text-blue-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                }`}
              >
                <span className="hidden md:inline">{tab.label}</span>
                <span className="md:hidden">{tab.shortLabel}</span>
              </button>
            ))}
          </div>
        </div>
      </section>

      {/* Content */}
      <section className="py-16">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          {renderContent()}
        </div>
      </section>
    </div>
  );
};

export default Nasmiles;
