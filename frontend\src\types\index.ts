// Flight Types
export interface Flight {
  _id: string;
  flightNumber: string;
  airline: {
    name: { ar: string; en: string };
    code: string;
    logo: string;
  };
  route: {
    from: {
      airport: { ar: string; en: string };
      city: { ar: string; en: string };
      code: string;
    };
    to: {
      airport: { ar: string; en: string };
      city: { ar: string; en: string };
      code: string;
    };
  };
  schedule: {
    departure: Date;
    arrival: Date;
    duration: string;
  };
  pricing: {
    economy: { adult: number; child: number; infant: number };
    business: { adult: number; child: number; infant: number };
    first: { adult: number; child: number; infant: number };
  };
  availability: {
    economy: number;
    business: number;
    first: number;
  };
  services: string[];
}

// User Types
export interface User {
  _id: string;
  firstName: string;
  lastName: string;
  email: string;
  phone: string;
  dateOfBirth: Date;
  nationality: string;
  loyaltyAccount?: string;
  bookings: string[];
  preferences: {
    language: 'ar' | 'en';
    currency: string;
    notifications: boolean;
  };
}

// Booking Types
export interface Booking {
  _id: string;
  reference: string;
  user: string;
  trip: {
    type: 'oneWay' | 'roundTrip' | 'multiCity';
    passengers: Passenger[];
  };
  flights: Flight[];
  pricing: {
    subtotal: number;
    taxes: number;
    fees: number;
    total: number;
    currency: string;
  };
  payment: {
    method: string;
    status: 'pending' | 'confirmed' | 'failed';
    transactionId?: string;
  };
  status: 'confirmed' | 'cancelled' | 'completed';
  services: {
    meals: string[];
    seats: string[];
    baggage: string[];
  };
  createdAt: Date;
}

export interface Passenger {
  title: 'Mr' | 'Mrs' | 'Ms';
  firstName: string;
  lastName: string;
  dateOfBirth: Date;
  nationality: string;
  passportNumber: string;
  type: 'adult' | 'child' | 'infant';
}

// Search Types
export interface FlightSearchParams {
  from: string;
  to: string;
  departure: Date;
  return?: Date;
  passengers: {
    adults: number;
    children: number;
    infants: number;
  };
  class: 'economy' | 'business' | 'first';
  tripType: 'oneWay' | 'roundTrip' | 'multiCity';
}

// Destination Types
export interface Destination {
  _id: string;
  name: { ar: string; en: string };
  code: string;
  country: { ar: string; en: string };
  continent: string;
  airports: Array<{
    name: { ar: string; en: string };
    code: string;
  }>;
  attractions: Array<{
    name: { ar: string; en: string };
    description: { ar: string; en: string };
    image: string;
  }>;
  weather: {
    season: string;
    temperature: { min: number; max: number };
    description: { ar: string; en: string };
  };
  visaRequired: boolean;
  currency: string;
  languages: string[];
}

// Hotel Types
export interface Hotel {
  _id: string;
  name: { ar: string; en: string };
  destination: string;
  location: {
    address: { ar: string; en: string };
    coordinates: { lat: number; lng: number };
  };
  rating: number;
  amenities: string[];
  roomTypes: Array<{
    type: { ar: string; en: string };
    price: number;
    capacity: number;
    amenities: string[];
  }>;
  images: string[];
  policies: {
    checkin: string;
    checkout: string;
    cancellation: { ar: string; en: string };
  };
}

// Loyalty Program Types
export interface LoyaltyAccount {
  _id: string;
  user: string;
  membershipNumber: string;
  tier: 'blue' | 'silver' | 'gold' | 'platinum';
  points: number;
  miles: number;
  benefits: string[];
  transactions: Array<{
    type: 'earn' | 'redeem';
    amount: number;
    description: { ar: string; en: string };
    date: Date;
  }>;
}

// API Response Types
export interface ApiResponse<T> {
  success: boolean;
  data: T;
  message?: string;
  errors?: string[];
}

export interface PaginatedResponse<T> {
  success: boolean;
  data: T[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    pages: number;
  };
}
