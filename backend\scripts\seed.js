#!/usr/bin/env node

const path = require('path');
require('dotenv').config({ path: path.join(__dirname, '..', '.env') });

const { seedData } = require('../utils/seedData');
const connectDB = require('../config/db');

const runSeed = async () => {
  try {
    console.log('🌱 بدء عملية إدخال البيانات التجريبية...');
    
    // Connect to database
    await connectDB();
    
    // Run seeding
    await seedData();
    
    console.log('✅ تم إدخال البيانات التجريبية بنجاح!');
    process.exit(0);
    
  } catch (error) {
    console.error('❌ خطأ في إدخال البيانات:', error.message);
    process.exit(1);
  }
};

runSeed();
