/**
 * Currency utility functions for SkyWays
 * Handles currency formatting, conversion, and configuration
 */

export interface CurrencyConfig {
  code: string;
  symbol: string;
  name: string;
  nameAr: string;
  position: 'before' | 'after';
  decimals: number;
  flag: string;
}

// Default currency configuration (USD)
export const DEFAULT_CURRENCY: CurrencyConfig = {
  code: 'USD',
  symbol: '$',
  name: 'US Dollar',
  nameAr: 'دولار أمريكي',
  position: 'before',
  decimals: 2,
  flag: '🇺🇸'
};

// Available currencies
export const CURRENCIES: Record<string, CurrencyConfig> = {
  USD: DEFAULT_CURRENCY,
  SAR: {
    code: 'SAR',
    symbol: 'ر.س',
    name: 'Saudi Riyal',
    nameAr: 'ريال سعودي',
    position: 'after',
    decimals: 2,
    flag: '🇸🇦'
  },
  EUR: {
    code: 'EUR',
    symbol: '€',
    name: 'Euro',
    nameAr: 'يورو',
    position: 'before',
    decimals: 2,
    flag: '🇪🇺'
  },
  AED: {
    code: 'AED',
    symbol: 'د.إ',
    name: 'UAE Dirham',
    nameAr: 'درهم إماراتي',
    position: 'after',
    decimals: 2,
    flag: '🇦🇪'
  }
};

// Currency conversion rates (for display purposes)
export const CURRENCY_RATES = {
  USD: 1,
  SAR: 3.75,
  EUR: 0.85,
  GBP: 0.73,
  AED: 3.67
};

// Default currency settings
export const DEFAULT_CURRENCY_CODE = 'USD';
export const DEFAULT_CURRENCY_SYMBOL = '$';

/**
 * Format currency amount with proper symbol and positioning
 */
export const formatCurrency = (
  amount: number,
  currencyCode: string = 'USD',
  locale: string = 'en-US'
): string => {
  const currency = CURRENCIES[currencyCode] || DEFAULT_CURRENCY;
  
  // Format number with proper decimals
  const formattedAmount = amount.toLocaleString(locale, {
    minimumFractionDigits: currency.decimals,
    maximumFractionDigits: currency.decimals
  });

  // Position symbol based on currency configuration
  if (currency.position === 'before') {
    return `${currency.symbol}${formattedAmount}`;
  } else {
    return `${formattedAmount} ${currency.symbol}`;
  }
};

/**
 * Format currency for Arabic locale
 */
export const formatCurrencyAr = (
  amount: number,
  currencyCode: string = 'USD'
): string => {
  return formatCurrency(amount, currencyCode, 'ar-SA');
};

/**
 * Get currency symbol
 */
export const getCurrencySymbol = (currencyCode: string = 'USD'): string => {
  return CURRENCIES[currencyCode]?.symbol || DEFAULT_CURRENCY.symbol;
};

/**
 * Get currency name
 */
export const getCurrencyName = (
  currencyCode: string = 'USD',
  locale: string = 'en'
): string => {
  const currency = CURRENCIES[currencyCode] || DEFAULT_CURRENCY;
  return locale === 'ar' ? currency.nameAr : currency.name;
};

/**
 * Get currency by code
 */
export const getCurrencyByCode = (code: string): CurrencyConfig => {
  return CURRENCIES[code] || DEFAULT_CURRENCY;
};

/**
 * Convert amount between currencies
 */
export const convertCurrency = (
  amount: number, 
  fromCurrency: string, 
  toCurrency: string
): number => {
  const fromRate = CURRENCY_RATES[fromCurrency as keyof typeof CURRENCY_RATES] || 1;
  const toRate = CURRENCY_RATES[toCurrency as keyof typeof CURRENCY_RATES] || 1;
  
  // Convert to USD first, then to target currency
  const usdAmount = amount / fromRate;
  return usdAmount * toRate;
};

/**
 * Convert price display text from SAR to USD
 */
export const convertPriceText = (text: string): string => {
  return text
    .replace(/ريال/g, 'دولار')
    .replace(/ر\.س/g, '$')
    .replace(/SAR/g, 'USD');
};

/**
 * Format price range
 */
export const formatPriceRange = (
  minPrice: number,
  maxPrice: number,
  currencyCode: string = 'USD',
  locale: string = 'en-US'
): string => {
  const min = formatCurrency(minPrice, currencyCode, locale);
  const max = formatCurrency(maxPrice, currencyCode, locale);
  return `${min} - ${max}`;
};

/**
 * Get supported currencies list
 */
export const getSupportedCurrencies = (): CurrencyConfig[] => {
  return Object.values(CURRENCIES);
};
