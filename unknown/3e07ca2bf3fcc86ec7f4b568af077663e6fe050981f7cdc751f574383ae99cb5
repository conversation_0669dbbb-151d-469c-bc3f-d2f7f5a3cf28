const express = require('express');
const { body, query } = require('express-validator');
const {
  searchCarRentals,
  getCarRentalById,
  getCarRentalsByDestination,
  getPartnerCarRentals,
  getVehicleCategories,
  createCarRental,
  updateCarRental,
  deleteCarRental
} = require('../controllers/carRentalController');
const { protect, authorize } = require('../middlewares/auth');

const router = express.Router();

// Validation middleware
const validateCarRentalSearch = [
  body('destination')
    .trim()
    .isLength({ min: 2 })
    .withMessage('الوجهة مطلوبة'),
  body('pickupDate')
    .optional()
    .isISO8601()
    .withMessage('تاريخ الاستلام غير صحيح'),
  body('returnDate')
    .optional()
    .isISO8601()
    .withMessage('تاريخ الإرجاع غير صحيح'),
  body('minPassengers')
    .optional()
    .isInt({ min: 1, max: 8 })
    .withMessage('عدد الركاب يجب أن يكون بين 1 و 8'),
  body('category')
    .optional()
    .isIn(['economy', 'compact', 'midsize', 'fullsize', 'luxury', 'suv', 'van'])
    .withMessage('فئة السيارة غير صحيحة'),
  body('transmission')
    .optional()
    .isIn(['manual', 'automatic'])
    .withMessage('نوع ناقل الحركة غير صحيح')
];

const validateCarRentalCreation = [
  body('company.name')
    .trim()
    .isLength({ min: 3 })
    .withMessage('اسم الشركة مطلوب'),
  body('company.code')
    .trim()
    .isLength({ min: 2, max: 5 })
    .withMessage('رمز الشركة مطلوب'),
  body('destination')
    .isMongoId()
    .withMessage('معرف الوجهة غير صحيح'),
  body('vehicles')
    .isArray({ min: 1 })
    .withMessage('يجب تحديد مركبة واحدة على الأقل'),
  body('vehicles.*.category')
    .isIn(['economy', 'compact', 'midsize', 'fullsize', 'luxury', 'suv', 'van'])
    .withMessage('فئة المركبة غير صحيحة'),
  body('vehicles.*.name')
    .trim()
    .isLength({ min: 3 })
    .withMessage('اسم المركبة مطلوب'),
  body('vehicles.*.capacity.passengers')
    .isInt({ min: 1, max: 8 })
    .withMessage('سعة الركاب مطلوبة'),
  body('vehicles.*.pricing.dailyRate')
    .isFloat({ min: 0 })
    .withMessage('السعر اليومي مطلوب')
];

const validatePagination = [
  query('page')
    .optional()
    .isInt({ min: 1 })
    .withMessage('رقم الصفحة يجب أن يكون رقم موجب'),
  query('limit')
    .optional()
    .isInt({ min: 1, max: 50 })
    .withMessage('عدد النتائج يجب أن يكون بين 1 و 50')
];

// Public routes
router.post('/search', validateCarRentalSearch, searchCarRentals);
router.get('/categories', getVehicleCategories);
router.get('/partners', getPartnerCarRentals);
router.get('/destination/:destinationId', getCarRentalsByDestination);
router.get('/:id', getCarRentalById);

// Admin routes
router.post('/', protect, authorize('admin'), validateCarRentalCreation, createCarRental);
router.put('/:id', protect, authorize('admin'), updateCarRental);
router.delete('/:id', protect, authorize('admin'), deleteCarRental);

module.exports = router;
