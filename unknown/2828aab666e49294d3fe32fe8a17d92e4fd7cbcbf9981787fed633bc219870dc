const express = require('express');
const { body, query } = require('express-validator');
const {
  searchAirportTransfers,
  getAirportTransferById,
  getTransfersByDestination,
  getPartnerTransfers,
  getServiceTypes,
  calculateTransferPrice,
  createAirportTransfer,
  updateAirportTransfer,
  deleteAirportTransfer
} = require('../controllers/airportTransferController');
const { protect, authorize } = require('../middlewares/auth');

const router = express.Router();

// Validation middleware
const validateTransferSearch = [
  body('destination')
    .trim()
    .isLength({ min: 2 })
    .withMessage('الوجهة مطلوبة'),
  body('transferType')
    .optional()
    .isIn(['arrival', 'departure'])
    .withMessage('نوع النقل غير صحيح'),
  body('passengers')
    .optional()
    .isInt({ min: 1, max: 20 })
    .withMessage('عدد الركاب يجب أن يكون بين 1 و 20'),
  body('serviceType')
    .optional()
    .isIn(['private-car', 'shared-shuttle', 'luxury-car', 'van', 'bus', 'taxi'])
    .withMessage('نوع الخدمة غير صحيح'),
  body('maxPrice')
    .optional()
    .isFloat({ min: 0 })
    .withMessage('الحد الأقصى للسعر يجب أن يكون رقم موجب')
];

const validatePriceCalculation = [
  body('transferId')
    .isMongoId()
    .withMessage('معرف خدمة النقل غير صحيح'),
  body('serviceType')
    .isIn(['private-car', 'shared-shuttle', 'luxury-car', 'van', 'bus', 'taxi'])
    .withMessage('نوع الخدمة مطلوب'),
  body('passengers')
    .isInt({ min: 1, max: 20 })
    .withMessage('عدد الركاب مطلوب'),
  body('extraStops')
    .optional()
    .isInt({ min: 0, max: 5 })
    .withMessage('عدد المحطات الإضافية يجب أن يكون بين 0 و 5'),
  body('waitingTime')
    .optional()
    .isInt({ min: 0, max: 300 })
    .withMessage('وقت الانتظار يجب أن يكون بين 0 و 300 دقيقة'),
  body('extras')
    .optional()
    .isArray()
    .withMessage('الخدمات الإضافية يجب أن تكون مصفوفة')
];

const validateTransferCreation = [
  body('destination')
    .isMongoId()
    .withMessage('معرف الوجهة غير صحيح'),
  body('provider.name')
    .trim()
    .isLength({ min: 3 })
    .withMessage('اسم مقدم الخدمة مطلوب'),
  body('provider.code')
    .trim()
    .isLength({ min: 2, max: 5 })
    .withMessage('رمز مقدم الخدمة مطلوب'),
  body('services')
    .isArray({ min: 1 })
    .withMessage('يجب تحديد خدمة واحدة على الأقل'),
  body('services.*.type')
    .isIn(['private-car', 'shared-shuttle', 'luxury-car', 'van', 'bus', 'taxi'])
    .withMessage('نوع الخدمة غير صحيح'),
  body('services.*.name')
    .trim()
    .isLength({ min: 3 })
    .withMessage('اسم الخدمة مطلوب'),
  body('services.*.vehicle.capacity.passengers')
    .isInt({ min: 1, max: 50 })
    .withMessage('سعة الركاب مطلوبة'),
  body('services.*.pricing.basePrice')
    .isFloat({ min: 0 })
    .withMessage('السعر الأساسي مطلوب'),
  body('services.*.duration.estimated')
    .isInt({ min: 1 })
    .withMessage('المدة المقدرة مطلوبة')
];

const validatePagination = [
  query('page')
    .optional()
    .isInt({ min: 1 })
    .withMessage('رقم الصفحة يجب أن يكون رقم موجب'),
  query('limit')
    .optional()
    .isInt({ min: 1, max: 50 })
    .withMessage('عدد النتائج يجب أن يكون بين 1 و 50')
];

// Public routes
router.post('/search', validateTransferSearch, searchAirportTransfers);
router.get('/service-types', getServiceTypes);
router.post('/calculate-price', validatePriceCalculation, calculateTransferPrice);
router.get('/partners', getPartnerTransfers);
router.get('/destination/:destinationId', getTransfersByDestination);
router.get('/:id', getAirportTransferById);

// Admin routes
router.post('/', protect, authorize('admin'), validateTransferCreation, createAirportTransfer);
router.put('/:id', protect, authorize('admin'), updateAirportTransfer);
router.delete('/:id', protect, authorize('admin'), deleteAirportTransfer);

module.exports = router;
