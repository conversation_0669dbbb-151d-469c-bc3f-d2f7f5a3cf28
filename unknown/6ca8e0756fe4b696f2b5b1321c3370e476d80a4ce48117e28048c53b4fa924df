const express = require('express');
const { body } = require('express-validator');
const {
  getCompanyInfoBySection,
  getAllCompanySections,
  getFleetDetails,
  getSustainabilityDetails,
  getCompanyStatistics,
  getCompanyTimeline,
  createCompanyInfo,
  updateCompanyInfo,
  deleteCompanyInfo
} = require('../controllers/companyInfoController');
const { protect, authorize } = require('../middlewares/auth');

const router = express.Router();

// Validation middleware
const validateCompanyInfoCreation = [
  body('section')
    .isIn(['about', 'fleet', 'sustainability', 'careers', 'news', 'awards'])
    .withMessage('قسم الشركة غير صحيح'),
  body('title.ar')
    .trim()
    .isLength({ min: 5 })
    .withMessage('العنوان باللغة العربية مطلوب'),
  body('title.en')
    .trim()
    .isLength({ min: 5 })
    .withMessage('العنوان باللغة الإنجليزية مطلوب'),
  body('content.ar')
    .trim()
    .isLength({ min: 20 })
    .withMessage('المحتوى باللغة العربية مطلوب'),
  body('content.en')
    .trim()
    .isLength({ min: 20 })
    .withMessage('المحتوى باللغة الإنجليزية مطلوب')
];

// Public routes
router.get('/statistics', getCompanyStatistics);
router.get('/timeline', getCompanyTimeline);
router.get('/fleet/details', getFleetDetails);
router.get('/sustainability/details', getSustainabilityDetails);
router.get('/:section', getCompanyInfoBySection);
router.get('/', getAllCompanySections);

// Admin routes
router.post('/', protect, authorize('admin'), validateCompanyInfoCreation, createCompanyInfo);
router.put('/:id', protect, authorize('admin'), updateCompanyInfo);
router.delete('/:id', protect, authorize('admin'), deleteCompanyInfo);

module.exports = router;
