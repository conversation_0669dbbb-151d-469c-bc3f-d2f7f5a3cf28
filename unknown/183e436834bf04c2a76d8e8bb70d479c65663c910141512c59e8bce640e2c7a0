const express = require('express');
const { body, query } = require('express-validator');
const {
  searchHotels,
  getHotelById,
  getHotelsByDestination,
  getPartnerHotels,
  createHotel,
  updateHotel,
  deleteHotel,
  getHotelStats
} = require('../controllers/hotelController');
const { protect, authorize } = require('../middlewares/auth');

const router = express.Router();

// Validation middleware
const validateHotelSearch = [
  body('destination')
    .trim()
    .isLength({ min: 2 })
    .withMessage('الوجهة مطلوبة'),
  body('checkIn')
    .optional()
    .isISO8601()
    .withMessage('تاريخ تسجيل الدخول غير صحيح'),
  body('checkOut')
    .optional()
    .isISO8601()
    .withMessage('تاريخ تسجيل الخروج غير صحيح'),
  body('guests')
    .optional()
    .isInt({ min: 1, max: 10 })
    .withMessage('عدد النزلاء يجب أن يكون بين 1 و 10'),
  body('rooms')
    .optional()
    .isInt({ min: 1, max: 5 })
    .withMessage('عدد الغرف يجب أن يكون بين 1 و 5')
];

const validateHotelCreation = [
  body('name')
    .trim()
    .isLength({ min: 3 })
    .withMessage('اسم الفندق مطلوب'),
  body('destination')
    .isMongoId()
    .withMessage('معرف الوجهة غير صحيح'),
  body('starRating')
    .isInt({ min: 1, max: 5 })
    .withMessage('تقييم النجوم يجب أن يكون بين 1 و 5'),
  body('address.city')
    .trim()
    .isLength({ min: 2 })
    .withMessage('المدينة مطلوبة'),
  body('address.country')
    .trim()
    .isLength({ min: 2 })
    .withMessage('البلد مطلوب'),
  body('roomTypes')
    .isArray({ min: 1 })
    .withMessage('يجب تحديد نوع غرفة واحد على الأقل'),
  body('roomTypes.*.name')
    .trim()
    .isLength({ min: 3 })
    .withMessage('اسم نوع الغرفة مطلوب'),
  body('roomTypes.*.capacity.adults')
    .isInt({ min: 1 })
    .withMessage('سعة البالغين مطلوبة'),
  body('roomTypes.*.pricing.basePrice')
    .isFloat({ min: 0 })
    .withMessage('السعر الأساسي مطلوب')
];

const validatePagination = [
  query('page')
    .optional()
    .isInt({ min: 1 })
    .withMessage('رقم الصفحة يجب أن يكون رقم موجب'),
  query('limit')
    .optional()
    .isInt({ min: 1, max: 50 })
    .withMessage('عدد النتائج يجب أن يكون بين 1 و 50')
];

// Public routes
router.post('/search', validateHotelSearch, searchHotels);
router.get('/partners', getPartnerHotels);
router.get('/destination/:destinationId', getHotelsByDestination);
router.get('/stats', protect, authorize('admin'), getHotelStats);
router.get('/:id', getHotelById);

// Admin routes
router.post('/', protect, authorize('admin'), validateHotelCreation, createHotel);
router.put('/:id', protect, authorize('admin'), updateHotel);
router.delete('/:id', protect, authorize('admin'), deleteHotel);

module.exports = router;
