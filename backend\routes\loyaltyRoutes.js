const express = require('express');
const { body, query } = require('express-validator');
const {
  getLoyaltyAccount,
  getLoyaltyTiers,
  redeemPoints,
  getLoyaltyTransactions,
  getAvailableRewards,
  getLoyaltyPartners
} = require('../controllers/loyaltyController');
const { protect } = require('../middlewares/auth');

const router = express.Router();

// Validation middleware
const validatePointsRedemption = [
  body('points')
    .isInt({ min: 1000 })
    .withMessage('الحد الأدنى للاستبدال 1000 نقطة'),
  body('rewardType')
    .trim()
    .isLength({ min: 3 })
    .withMessage('نوع المكافأة مطلوب'),
  body('description')
    .optional()
    .trim()
    .isLength({ max: 200 })
    .withMessage('الوصف يجب أن يكون أقل من 200 حرف')
];

const validatePagination = [
  query('page')
    .optional()
    .isInt({ min: 1 })
    .withMessage('رقم الصفحة يجب أن يكون رقم موجب'),
  query('limit')
    .optional()
    .isInt({ min: 1, max: 100 })
    .withMessage('عدد النتائج يجب أن يكون بين 1 و 100')
];

// Public routes
router.get('/tiers', getLoyaltyTiers);
router.get('/partners', getLoyaltyPartners);

// Protected routes
router.use(protect); // All routes below require authentication

router.get('/account', getLoyaltyAccount);
router.get('/rewards', getAvailableRewards);
router.get('/transactions', validatePagination, getLoyaltyTransactions);
router.post('/redeem', validatePointsRedemption, redeemPoints);

module.exports = router;
