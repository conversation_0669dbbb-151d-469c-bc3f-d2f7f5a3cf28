{"zencoder.enableRepoIndexing": true, "css.validate": false, "less.validate": false, "scss.validate": false, "files.associations": {"*.css": "tailwindcss"}, "editor.quickSuggestions": {"strings": true}, "css.customData": [".vscode/css_custom_data.json"], "tailwindCSS.includeLanguages": {"html": "html", "javascript": "javascript", "typescript": "typescript", "javascriptreact": "javascript", "typescriptreact": "typescript"}, "tailwindCSS.experimental.classRegex": ["tw`([^`]*)", ["clsx\\(([^)]*)\\)", "(?:'|\"|`)([^']*)(?:'|\"|`)"], ["classnames\\(([^)]*)\\)", "(?:'|\"|`)([^']*)(?:'|\"|`)"], ["cva\\(([^)]*)\\)", "[\"'`]([^\"'`]*).*?[\"'`]"], ["cn\\(([^)]*)\\)", "(?:'|\"|`)([^']*)(?:'|\"|`)"]], "emmet.includeLanguages": {"javascript": "javascriptreact", "typescript": "typescriptreact"}, "editor.inlineSuggest.enabled": true}