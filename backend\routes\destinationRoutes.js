const express = require('express');
const { query } = require('express-validator');
const {
  getAllDestinations,
  getDestination,
  searchDestinations,
  getPopularDestinations,
  getDestinationsByContinent,
  createDestination,
  updateDestination,
  deleteDestination,
  getDestinationStats
} = require('../controllers/destinationController');
const { protect, authorize } = require('../middlewares/auth');

const router = express.Router();

// Validation middleware
const validateSearch = [
  query('q')
    .isLength({ min: 2 })
    .withMessage('البحث يجب أن يكون على الأقل حرفين'),
  query('limit')
    .optional()
    .isInt({ min: 1, max: 50 })
    .withMessage('الحد الأقصى للنتائج يجب أن يكون بين 1 و 50')
];

const validatePagination = [
  query('page')
    .optional()
    .isInt({ min: 1 })
    .withMessage('رقم الصفحة يجب أن يكون رقم موجب'),
  query('limit')
    .optional()
    .isInt({ min: 1, max: 100 })
    .withMessage('عدد النتائج يجب أن يكون بين 1 و 100')
];

// Public routes
router.get('/search', validateSearch, searchDestinations);
router.get('/popular', getPopularDestinations);
router.get('/continent/:continent', getDestinationsByContinent);
router.get('/stats', protect, authorize('admin'), getDestinationStats);
router.get('/:identifier', getDestination);
router.get('/', validatePagination, getAllDestinations);

// Admin routes
router.post('/', protect, authorize('admin'), createDestination);
router.put('/:id', protect, authorize('admin'), updateDestination);
router.delete('/:id', protect, authorize('admin'), deleteDestination);

module.exports = router;
