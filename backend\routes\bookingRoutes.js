const express = require('express');
const { body, query } = require('express-validator');
const {
  createBooking,
  getUserBookings,
  getBookingById,
  getBookingByReference,
  updateBooking,
  cancelBooking,
  confirmPayment
} = require('../controllers/bookingController');
const { protect } = require('../middlewares/auth');

const router = express.Router();

// Validation middleware
const validateBookingCreation = [
  body('tripType')
    .isIn(['one-way', 'round-trip', 'multi-city'])
    .withMessage('نوع الرحلة غير صحيح'),
  body('flights')
    .isArray({ min: 1 })
    .withMessage('يجب تحديد رحلة واحدة على الأقل'),
  body('flights.*.flight')
    .isMongoId()
    .withMessage('معرف الرحلة غير صحيح'),
  body('flights.*.class')
    .isIn(['economy', 'business', 'first'])
    .withMessage('درجة السفر غير صحيحة'),
  body('flights.*.passengers')
    .isArray({ min: 1 })
    .withMessage('يجب تحديد مسافر واحد على الأقل'),
  body('flights.*.passengers.*.firstName')
    .trim()
    .isLength({ min: 2 })
    .withMessage('الاسم الأول مطلوب'),
  body('flights.*.passengers.*.lastName')
    .trim()
    .isLength({ min: 2 })
    .withMessage('اسم العائلة مطلوب'),
  body('flights.*.passengers.*.dateOfBirth')
    .isISO8601()
    .withMessage('تاريخ الميلاد غير صحيح'),
  body('flights.*.passengers.*.nationality')
    .trim()
    .isLength({ min: 2 })
    .withMessage('الجنسية مطلوبة'),
  body('flights.*.passengers.*.passportNumber')
    .trim()
    .isLength({ min: 6 })
    .withMessage('رقم الجواز غير صحيح'),
  body('contactInfo.email')
    .isEmail()
    .withMessage('البريد الإلكتروني غير صحيح'),
  body('contactInfo.phone')
    .matches(/^[+]?[1-9][\d]{7,14}$/)
    .withMessage('رقم الهاتف غير صحيح')
];

const validateBookingUpdate = [
  body('contactInfo.email')
    .optional()
    .isEmail()
    .withMessage('البريد الإلكتروني غير صحيح'),
  body('contactInfo.phone')
    .optional()
    .matches(/^[+]?[1-9][\d]{7,14}$/)
    .withMessage('رقم الهاتف غير صحيح'),
  body('specialRequests')
    .optional()
    .isArray()
    .withMessage('الطلبات الخاصة يجب أن تكون مصفوفة')
];

const validatePaymentConfirmation = [
  body('transactionId')
    .trim()
    .isLength({ min: 10 })
    .withMessage('معرف المعاملة غير صحيح'),
  body('paymentMethod')
    .isIn(['credit-card', 'debit-card', 'paypal', 'bank-transfer'])
    .withMessage('طريقة الدفع غير صحيحة')
];

const validateBookingReference = [
  query('email')
    .isEmail()
    .withMessage('البريد الإلكتروني مطلوب وصحيح')
];

const validatePagination = [
  query('page')
    .optional()
    .isInt({ min: 1 })
    .withMessage('رقم الصفحة يجب أن يكون رقم موجب'),
  query('limit')
    .optional()
    .isInt({ min: 1, max: 50 })
    .withMessage('عدد النتائج يجب أن يكون بين 1 و 50')
];

// Public routes
router.get('/reference/:reference', validateBookingReference, getBookingByReference);

// Protected routes
router.use(protect); // All routes below require authentication

router.post('/', validateBookingCreation, createBooking);
router.get('/', validatePagination, getUserBookings);
router.get('/:id', getBookingById);
router.put('/:id', validateBookingUpdate, updateBooking);
router.delete('/:id', cancelBooking);
router.post('/:id/confirm-payment', validatePaymentConfirmation, confirmPayment);

module.exports = router;
