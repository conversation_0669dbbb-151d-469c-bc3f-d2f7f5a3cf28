const express = require('express');
const { body, query } = require('express-validator');
const {
  getActiveOffers,
  getOfferById,
  applyPromoCode,
  getFeaturedOffers,
  getOffersByType,
  createOffer,
  updateOffer,
  deleteOffer
} = require('../controllers/offerController');
const { protect, authorize } = require('../middlewares/auth');

const router = express.Router();

// Validation middleware
const validatePromoCode = [
  body('promoCode')
    .trim()
    .isLength({ min: 3, max: 20 })
    .withMessage('كود الخصم يجب أن يكون بين 3 و 20 حرف'),
  body('bookingAmount')
    .isFloat({ min: 1 })
    .withMessage('مبلغ الحجز يجب أن يكون رقم موجب')
];

const validateOfferCreation = [
  body('title.ar')
    .trim()
    .isLength({ min: 5 })
    .withMessage('عنوان العرض باللغة العربية مطلوب'),
  body('title.en')
    .trim()
    .isLength({ min: 5 })
    .withMessage('عنوان العرض باللغة الإنجليزية مطلوب'),
  body('description.ar')
    .trim()
    .isLength({ min: 10 })
    .withMessage('وصف العرض باللغة العربية مطلوب'),
  body('description.en')
    .trim()
    .isLength({ min: 10 })
    .withMessage('وصف العرض باللغة الإنجليزية مطلوب'),
  body('type')
    .isIn(['flight-discount', 'package-deal', 'loyalty-bonus', 'seasonal', 'last-minute', 'group-booking'])
    .withMessage('نوع العرض غير صحيح'),
  body('discount.type')
    .isIn(['percentage', 'fixed-amount', 'buy-one-get-one'])
    .withMessage('نوع الخصم غير صحيح'),
  body('discount.value')
    .isFloat({ min: 0 })
    .withMessage('قيمة الخصم يجب أن تكون رقم موجب'),
  body('validity.startDate')
    .isISO8601()
    .withMessage('تاريخ بداية العرض غير صحيح'),
  body('validity.endDate')
    .isISO8601()
    .withMessage('تاريخ نهاية العرض غير صحيح')
];

const validatePagination = [
  query('page')
    .optional()
    .isInt({ min: 1 })
    .withMessage('رقم الصفحة يجب أن يكون رقم موجب'),
  query('limit')
    .optional()
    .isInt({ min: 1, max: 50 })
    .withMessage('عدد النتائج يجب أن يكون بين 1 و 50')
];

// Public routes
router.get('/featured', getFeaturedOffers);
router.get('/type/:type', getOffersByType);
router.get('/:id', getOfferById);
router.get('/', validatePagination, getActiveOffers);

// Protected routes
router.post('/apply-promo', protect, validatePromoCode, applyPromoCode);

// Admin routes
router.post('/', protect, authorize('admin'), validateOfferCreation, createOffer);
router.put('/:id', protect, authorize('admin'), updateOffer);
router.delete('/:id', protect, authorize('admin'), deleteOffer);

module.exports = router;
