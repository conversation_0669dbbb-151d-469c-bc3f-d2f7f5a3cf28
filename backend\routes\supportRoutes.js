const express = require('express');
const { body, query } = require('express-validator');
const {
  getFAQs,
  getFAQCategories,
  getContactInfo,
  createSupportTicket,
  getUserSupportTickets,
  getSupportTicketByNumber,
  getSpecialServices,
  getSpecialServiceCategories,
  markFAQHelpful
} = require('../controllers/supportController');
const { protect } = require('../middlewares/auth');

const router = express.Router();

// Validation middleware
const validateTicketCreation = [
  body('category')
    .isIn(['booking-issue', 'payment-problem', 'baggage-claim', 'refund-request', 'complaint', 'suggestion', 'technical-issue'])
    .withMessage('فئة التذكرة غير صحيحة'),
  body('subject')
    .trim()
    .isLength({ min: 5, max: 200 })
    .withMessage('الموضوع يجب أن يكون بين 5 و 200 حرف'),
  body('description')
    .trim()
    .isLength({ min: 20, max: 2000 })
    .withMessage('الوصف يجب أن يكون بين 20 و 2000 حرف'),
  body('bookingReference')
    .optional()
    .trim()
    .isLength({ min: 6, max: 10 })
    .withMessage('رقم الحجز غير صحيح'),
  body('guestInfo.name')
    .if(body('guestInfo').exists())
    .trim()
    .isLength({ min: 2 })
    .withMessage('الاسم مطلوب للضيوف'),
  body('guestInfo.email')
    .if(body('guestInfo').exists())
    .isEmail()
    .withMessage('البريد الإلكتروني غير صحيح'),
  body('guestInfo.phone')
    .if(body('guestInfo').exists())
    .trim()
    .isLength({ min: 8 })
    .withMessage('رقم الهاتف مطلوب')
];

const validateFAQSearch = [
  query('category')
    .optional()
    .isIn(['booking', 'payment', 'baggage', 'check-in', 'loyalty', 'refunds', 'general'])
    .withMessage('فئة الأسئلة غير صحيحة'),
  query('search')
    .optional()
    .trim()
    .isLength({ min: 2 })
    .withMessage('كلمة البحث يجب أن تكون حرفين على الأقل'),
  query('limit')
    .optional()
    .isInt({ min: 1, max: 100 })
    .withMessage('عدد النتائج يجب أن يكون بين 1 و 100')
];

const validateContactSearch = [
  query('type')
    .optional()
    .isIn(['office', 'call-center', 'airport', 'sales'])
    .withMessage('نوع الاتصال غير صحيح'),
  query('country')
    .optional()
    .trim()
    .isLength({ min: 2 })
    .withMessage('اسم البلد يجب أن يكون حرفين على الأقل')
];

const validateSpecialServiceSearch = [
  query('category')
    .optional()
    .isIn(['elderly', 'children', 'pregnant', 'disabled', 'medical', 'dietary'])
    .withMessage('فئة الخدمة الخاصة غير صحيحة')
];

const validateFAQHelpful = [
  body('helpful')
    .isBoolean()
    .withMessage('تقييم الفائدة يجب أن يكون true أو false')
];

const validatePagination = [
  query('page')
    .optional()
    .isInt({ min: 1 })
    .withMessage('رقم الصفحة يجب أن يكون رقم موجب'),
  query('limit')
    .optional()
    .isInt({ min: 1, max: 50 })
    .withMessage('عدد النتائج يجب أن يكون بين 1 و 50')
];

// Public routes
router.get('/faq/categories', getFAQCategories);
router.get('/faq', validateFAQSearch, getFAQs);
router.post('/faq/:id/helpful', validateFAQHelpful, markFAQHelpful);

router.get('/contact', validateContactSearch, getContactInfo);

router.get('/special-services/categories', getSpecialServiceCategories);
router.get('/special-services', validateSpecialServiceSearch, getSpecialServices);

router.post('/tickets', validateTicketCreation, createSupportTicket);
router.get('/tickets/:ticketNumber', getSupportTicketByNumber);

// Protected routes
router.get('/tickets', protect, validatePagination, getUserSupportTickets);

module.exports = router;
